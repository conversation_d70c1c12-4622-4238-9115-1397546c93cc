{"name": "api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "node copy-locations.js && nest start --watch", "start:debug": "node copy-locations.js && nest start --debug --watch", "start:prod": "node copy-locations.js && node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "dotenv -e .env.test -- jest --config ./test/jest-e2e.json", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "typeorm:generate-migration": "npm run typeorm migration:generate -- -d ./src/__shared__/config/typeorm.config.ts ./src/__migrations__/$npm_config_name", "typeorm:run-migration": "npm run build && npm run typeorm migration:run -- -d ./src/__shared__/config/typeorm.config.ts", "typeorm migration:revert": "npm run build && npm run typeorm migration:revert -- -d ./src/__shared__/config/typeorm.config.ts", "prepare": "husky &", "seed:locations": "node copy-locations.js", "seed:data": "ts-node -r tsconfig-paths/register src/seeds/seed.ts"}, "dependencies": {"@nestjs/axios": "^3.1.2", "@nestjs/common": "^10.4.1", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.4.1", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.1", "@nestjs/swagger": "^7.4.0", "@nestjs/terminus": "^10.2.3", "@nestjs/typeorm": "^10.0.2", "@sendgrid/mail": "^8.1.3", "@types/puppeteer": "^5.4.7", "api": "file:", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.6", "dotenv": "^16.4.5", "handlebars": "^4.7.8", "helmet": "^7.1.0", "html-pdf": "^3.0.1", "nestjs-typeorm-paginate": "^4.0.4", "nodemailer": "^6.9.15", "passport-jwt": "^4.0.1", "pdfkit": "^0.15.1", "pg": "^8.12.0", "puppeteer": "^23.11.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "typeorm": "^0.3.20"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.4.1", "@testcontainers/postgresql": "^10.2.1", "@types/bcryptjs": "^2.4.4", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/pdfkit": "^0.13.7", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "dotenv-cli": "^7.4.2", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^9.1.6", "jest": "^29.7.0", "lint-staged": "^15.2.10", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "lint-staged": {"**/*.{ts,js}": "prettier --write"}}